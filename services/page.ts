import { LandingPage, PricingPage, ShowcasePage } from "@/types/pages/landing";
import { V3PromptGeneratorPage } from "@/types/pages/veo3-prompt-generator";
import { Veo3PromptLibraryPage } from "@/types/pages/veo3-prompt-library";
import { Veo3VideoGeneratorPage } from "@/types/pages/veo3-video-generator";
import { AboutPage } from "@/types/pages/about";
import { ContactPageTranslations } from "@/types/pages/contact";
import { ChatgptPromptGeneratorPage } from "@/types/pages/chatgpt-prompt-generator";
import { LyraPromptGeneratorPage } from "@/types/pages/lyra-prompt-generator";
import { NanoBananaPromptLibraryPage } from "@/types/pages/nano-banana-prompt-library";

export async function getLandingPage(locale: string): Promise<LandingPage> {
  return (await getPage("landing", locale)) as LandingPage;
}

export async function getPricingPage(locale: string): Promise<PricingPage> {
  return (await getPage("pricing", locale)) as PricingPage;
}

export async function getShowcasePage(locale: string): Promise<ShowcasePage> {
  return (await getPage("showcase", locale)) as ShowcasePage;
}

export async function getVeo3PromptGeneratorPage(locale: string): Promise<V3PromptGeneratorPage> {
  return (await getPage("veo-3-prompt-generator", locale)) as V3PromptGeneratorPage;
}

export async function getVeo3PromptLibraryPage(locale: string): Promise<Veo3PromptLibraryPage> {
  return (await getPage("veo-3-prompt-library", locale)) as Veo3PromptLibraryPage;
}

export async function getVeo3VideoGeneratorPage(locale: string): Promise<Veo3VideoGeneratorPage> {
  return (await getPage("veo-3-video-generator", locale)) as Veo3VideoGeneratorPage;
}

export async function getAboutPage(locale: string): Promise<AboutPage> {
  return (await getPage("about", locale)) as AboutPage;
}

export async function getContactPage(locale: string): Promise<ContactPageTranslations> {
  return (await getPage("contact", locale)) as ContactPageTranslations;
}

export async function getChatgptPromptGeneratorPage(locale: string): Promise<ChatgptPromptGeneratorPage> {
  return (await getPage("chatgpt-prompt-generator", locale)) as ChatgptPromptGeneratorPage;
}

export async function getLyraPromptGeneratorPage(locale: string): Promise<LyraPromptGeneratorPage> {
  return (await getPage("lyra-prompt-generator", locale)) as LyraPromptGeneratorPage;
}

export async function getNanoBananaPromptLibraryPage(locale: string): Promise<NanoBananaPromptLibraryPage> {
  return (await getPage("nano-banana-prompt-library", locale)) as NanoBananaPromptLibraryPage;
}

export async function getPage(
  name: string,
  locale: string
): Promise<LandingPage | PricingPage | ShowcasePage | V3PromptGeneratorPage | Veo3PromptLibraryPage | Veo3VideoGeneratorPage | AboutPage | ContactPageTranslations | ChatgptPromptGeneratorPage | LyraPromptGeneratorPage | NanoBananaPromptLibraryPage> {
  try {
    if (locale === "zh-CN") {
      locale = "zh";
    }

    return await import(
      `@/i18n/pages/${name}/${locale.toLowerCase()}.json`
    ).then((module) => module.default);
  } catch (error) {
    console.warn(`Failed to load ${locale}.json, falling back to en.json`);

    return await import(`@/i18n/pages/${name}/en.json`).then(
      (module) => module.default
    );
  }
}
