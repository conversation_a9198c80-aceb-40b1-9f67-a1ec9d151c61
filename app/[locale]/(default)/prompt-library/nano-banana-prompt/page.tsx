import Hero from "@/components/blocks/hero";
import CTA from "@/components/blocks/cta";
import ImageEditExamples from "@/components/blocks/image-edit-examples";
import { getNanoBananaPromptLibraryPage } from "@/services/page";
import { getTranslations } from "next-intl/server";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/prompt-library/nano-banana-prompt`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/prompt-library/nano-banana-prompt`;
  }

  const page = await getNanoBananaPromptLibraryPage(locale);

  return {
    title: page.title,
    description: page.description,
    keywords: "",
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function NanoBananaPromptLibraryPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getNanoBananaPromptLibraryPage(locale);
  const t = await getTranslations("enhanced_prompt_generator");

  const examplesConfig = {
    copyButton: t("copyButton"),
    successCopied: t("successCopied"),
    errorCopyFailed: t("errorCopyFailed"),
    viewLargeImage: locale === "zh" ? "查看大图" : "View Large Image",
    beforeLabel: locale === "zh" ? "编辑前" : "Before",
    afterLabel: locale === "zh" ? "编辑后" : "After",
  };

  return (
    <>
      {page.hero && <Hero hero={page.hero} />}
      {page.examples && (
        <ImageEditExamples 
          section={page.examples}
          config={examplesConfig}
          className="py-16"
        />
      )}
      {page.cta && <CTA section={page.cta} />}
    </>
  );
}
