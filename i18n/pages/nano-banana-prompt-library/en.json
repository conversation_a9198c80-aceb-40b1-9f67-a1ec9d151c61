{"template": "shipany-template-one", "theme": "light", "title": "Best Nano Banana Prompts - Gemini 2.5 Flash Image Prompt", "description": "Discover the best Nano Banana prompts collection with professional image editing examples. <PERSON><PERSON><PERSON> curated Gemini 2.5 Flash Image prompt examples for AI image editing.", "header": {"brand": {"title": "Prompt Ark", "logo": {"src": "/logo.png", "alt": "Prompt Ark"}, "url": "/"}, "nav": {"items": [{"title": "Nano Banana Prompt Library", "url": "/prompt-library/nano-banana-prompt", "icon": "RiImageEditLine"}, {"title": "VEO-3 Prompt Library", "url": "/prompt-library/veo-3-prompts", "icon": "RiMagicLine"}], "buttons": [{"title": "Sign In", "variant": "primary", "href": "/auth/signin"}]}, "show_sign": true, "show_theme": true, "show_locale": true}, "hero": {"title": "Best Nano Banana Prompts & Image Editing Examples", "highlight_text": "Best <PERSON><PERSON> Prompts", "description": "Discover the best prompts for Nan<PERSON> Banana (Google Gemini 2.5 Flash Image) with our curated collection of image editing examples<br/>Professional template prompts designed for AI creators and developers seeking top-quality image editing results", "announcement": {"label": "Nano Banana Prompt Examples", "title": "🎨 Best Nano Banana Image Editing Prompts Collection", "url": "/prompt-library/nano-banana-prompt#examples"}, "tip": "🖼️ Curated <PERSON><PERSON> prompt examples for professional AI image editing", "buttons": [{"title": "Browse Best <PERSON><PERSON> Prompts", "icon": "RiImageEditLine", "url": "#examples", "target": "_self", "variant": "default"}], "show_happy_users": false, "show_badge": false}, "examples": {"name": "examples", "title": "Best Nano Banana Prompt Examples & Templates", "subtitle": "If you have more interesting <PERSON><PERSON> prompt examples, please share with us (email: <EMAIL>).", "items": [{"id": "example-1", "title": "Photo to <PERSON><PERSON><PERSON> Style", "description": "This nano banana prompt turns photos into custom miniature figurine images", "category": "Style Transfer", "beforeImage": "https://r2.promptark.net/nano-banana/photo-to-figurine-style-before.webp", "afterImage": "https://r2.promptark.net/nano-banana/photo-to-figurine-style-after.webp", "prompt": "Create a 1/7 scale commercialized figurine of the characters in the picture, in a realistic style, in a real environment. The figurine is placed on a computer desk. The figurine has a round transparent acrylic base, with no text on the base. The content on the computer screen is a 3D modeling process of this figurine. Next to the computer screen is a toy packaging box, designed in a style reminiscent of high-quality collectible figures, printed with original artwork. The packaging features two-dimensional flat illustrations.", "tags": ["figurine"]}, {"id": "example-2", "title": "Cosplay Photo", "description": "This nano banana prompt turns anime character into cosplay photos", "category": "Style Transfer", "beforeImage": "https://r2.promptark.net/nano-banana/cosplay-photo-before.webp", "afterImage": "https://r2.promptark.net/nano-banana/cosplay-photo-after.webp", "prompt": "Generate a highly detailed photo of a girl cosplaying this illustration, at Comiket. Exactly replicate the same pose, body posture, hand gestures, facial expression, and camera framing as in the original illustration. Keep the same angle, perspective, and composition, without any deviation", "tags": ["cosplay", "anime"]}, {"id": "example-3", "title": "Sculpture", "description": "This nano banana prompt turns photos into marble sculptures", "category": "Style Transfer", "beforeImage": "https://r2.promptark.net/nano-banana/sculpture-before.webp", "afterImage": "https://r2.promptark.net/nano-banana/sculpture-after.webp", "prompt": "A photorealistic image of an ultra-detailed sculpture of the subject in image made of shining marble. The sculpture should display smooth and reflective marble surface, emphasizing its luster and artistic craftsmanship. The design is elegant, highlighting the beauty and depth of marble. The lighting in the image should enhance the sculpture's contours and textures, creating a visually stunning and mesmerizing effect", "tags": ["sculpture"]}, {"id": "example-4", "title": "Turn illustration into realistic version", "description": "This nano banana prompt turns illustration into realistic version", "category": "Style Transfer", "beforeImage": "https://r2.promptark.net/nano-banana/realistic-before.webp", "afterImage": "https://r2.promptark.net/nano-banana/realistic-after.webp", "prompt": "turn this illustration into realistic version", "tags": ["realistic"]}, {"id": "example-5", "title": "Change pose", "description": "This nano banana prompt changes the pose of the characters", "category": "Enhancment", "beforeImage": "https://r2.promptark.net/nano-banana/change-pose-before.webp", "afterImage": "https://r2.promptark.net/nano-banana/change-pose-after.webp", "prompt": "Change characters to input poses", "tags": ["pose"]}]}, "cta": {"disabled": true, "name": "cta", "title": "Start Creating with <PERSON><PERSON>", "description": "Use Google Gemini 2.5 Flash Image (Nano <PERSON>) to edit your images with AI. Perfect for creators and developers who want professional image editing results.", "buttons": [{"title": "Try <PERSON>o <PERSON>", "url": "https://aistudio.google.com/", "target": "_blank", "icon": "RiExternalLinkLine"}, {"title": "Learn More", "url": "#examples", "target": "_self", "variant": "outline"}]}}