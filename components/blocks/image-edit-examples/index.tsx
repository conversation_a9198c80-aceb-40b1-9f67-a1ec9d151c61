import { ImageEditExamplesProps } from "@/types/blocks/image-edit-examples";
import ImageEditCard from "./image-edit-card";
import { cn } from "@/lib/utils";

export default function ImageEditExamples({ 
  section, 
  config, 
  className 
}: ImageEditExamplesProps) {
  if (!section.items || section.items.length === 0) {
    return null;
  }

  return (
    <section className={cn("py-16", className)} id="examples">
      <div className="container mx-auto px-4">
        {/* 标题区域 */}
        <div className="text-center mb-12">
          <h2 className="text-xl font-bold mb-4">{section.title}</h2>
          {section.subtitle && (
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              {section.subtitle}
            </p>
          )}
        </div>
        
        {/* 示例网格 */}
        <div className="space-y-8">
          {section.items.map((example) => (
            <ImageEditCard
              key={example.id}
              example={example}
              config={config}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
