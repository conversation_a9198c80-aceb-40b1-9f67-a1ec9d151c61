export interface ImageEditExample {
  id: string;
  title: string;
  description: string;
  category: string;
  beforeImage: string;
  afterImage: string;
  prompt: string;
  tags?: string[];
}

export interface ImageEditExamplesSection {
  title: string;
  subtitle?: string;
  items: ImageEditExample[];
}

export interface ImageEditExamplesConfig {
  copyButton: string;
  successCopied: string;
  errorCopyFailed: string;
  viewLargeImage: string;
  beforeLabel: string;
  afterLabel: string;
}

export interface ImageEditExamplesProps {
  section: ImageEditExamplesSection;
  config: ImageEditExamplesConfig;
  className?: string;
}
