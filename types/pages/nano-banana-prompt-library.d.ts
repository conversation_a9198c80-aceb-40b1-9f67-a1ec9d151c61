import { Header } from "@/types/blocks/header";
import { Hero } from "@/types/blocks/hero";
import { Section } from "@/types/blocks/section";
import { Footer } from "@/types/blocks/footer";
import { ImageEditExamplesSection } from "@/types/blocks/image-edit-examples";

export interface NanoBananaPromptLibraryPage {
  title?: string;
  description?: string;
  header?: Header;
  hero?: Hero;
  examples?: ImageEditExamplesSection;
  cta?: Section;
  footer?: Footer;
}
