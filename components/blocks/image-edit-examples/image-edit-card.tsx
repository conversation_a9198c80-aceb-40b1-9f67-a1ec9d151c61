"use client";

import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Copy, ArrowRight, ZoomIn } from "lucide-react";
import { toast } from "sonner";
import { ImageEditExample, ImageEditExamplesConfig } from "@/types/blocks/image-edit-examples";
import { useState } from "react";
import Image from "next/image";

interface ImageEditCardProps {
  example: ImageEditExample;
  config: ImageEditExamplesConfig;
}

export default function ImageEditCard({ example, config }: ImageEditCardProps) {
  const [isImageOpen, setIsImageOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState<'before' | 'after'>('before');

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(example.prompt);
      toast.success(config.successCopied);
    } catch (error) {
      toast.error(config.errorCopyFailed);
    }
  };

  const openImageDialog = (imageType: 'before' | 'after') => {
    setSelectedImage(imageType);
    setIsImageOpen(true);
  };

  return (
    <Card className="overflow-hidden">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">{example.title}</h3>
          <span className="text-sm bg-primary/10 text-primary px-2 py-1 rounded-full">
            {example.category}
          </span>
        </div>
        {example.description && (
          <p className="text-sm text-muted-foreground">{example.description}</p>
        )}
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* 图片对比区域 */}
        <div className="flex flex-col lg:flex-row gap-6 items-center">
          {/* Before 图片 */}
          <div className="flex-1 space-y-2">
            <div className="text-sm font-medium text-center">{config.beforeLabel}</div>
            <div
              className="relative aspect-[4/3] rounded-lg overflow-hidden bg-muted cursor-pointer group max-w-md mx-auto lg:max-w-none"
              onClick={() => openImageDialog('before')}
            >
              <Image
                src={example.beforeImage}
                alt={`${example.title} - Before`}
                fill
                className="object-cover transition-transform group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center">
                <ZoomIn className="h-6 w-6 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
              </div>
            </div>
          </div>

          {/* 箭头 */}
          <div className="flex justify-center lg:flex-shrink-0">
            <ArrowRight className="h-8 w-8 text-muted-foreground rotate-90 lg:rotate-0" />
          </div>

          {/* After 图片 */}
          <div className="flex-1 space-y-2">
            <div className="text-sm font-medium text-center">{config.afterLabel}</div>
            <div
              className="relative aspect-[4/3] rounded-lg overflow-hidden bg-muted cursor-pointer group max-w-md mx-auto lg:max-w-none"
              onClick={() => openImageDialog('after')}
            >
              <Image
                src={example.afterImage}
                alt={`${example.title} - After`}
                fill
                className="object-cover transition-transform group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center">
                <ZoomIn className="h-6 w-6 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
              </div>
            </div>
          </div>
        </div>
        
        {/* 提示词区域 */}
        <div className="space-y-3">
          <div className="relative">
            <pre className="bg-muted border rounded-lg p-4 text-sm overflow-x-auto whitespace-pre-wrap max-h-40 overflow-y-auto text-foreground">
              <code className="text-foreground">{example.prompt}</code>
            </pre>
            <Button
              size="sm"
              variant="outline"
              onClick={handleCopy}
              className="absolute top-2 right-2 h-8 w-8 p-0"
            >
              <Copy className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="text-center">
            <Button
              onClick={handleCopy}
              variant="outline"
              size="sm"
              className="w-full"
            >
              <Copy className="h-4 w-4 mr-2" />
              {config.copyButton}
            </Button>
          </div>
        </div>

        {/* 图片预览对话框 */}
        <Dialog open={isImageOpen} onOpenChange={setIsImageOpen}>
          <DialogContent className="max-w-4xl">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-center">
                {example.title} - {selectedImage === 'before' ? config.beforeLabel : config.afterLabel}
              </h3>
              <div className="relative aspect-[4/3] rounded-lg overflow-hidden">
                <Image
                  src={selectedImage === 'before' ? example.beforeImage : example.afterImage}
                  alt={`${example.title} - ${selectedImage === 'before' ? 'Before' : 'After'}`}
                  fill
                  className="object-contain"
                />
              </div>
              <div className="flex justify-center gap-2">
                <Button
                  variant={selectedImage === 'before' ? 'default' : 'outline'}
                  onClick={() => setSelectedImage('before')}
                >
                  {config.beforeLabel}
                </Button>
                <Button
                  variant={selectedImage === 'after' ? 'default' : 'outline'}
                  onClick={() => setSelectedImage('after')}
                >
                  {config.afterLabel}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}
