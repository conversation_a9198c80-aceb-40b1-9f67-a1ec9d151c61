# Nano Banana 提示词库页面需求文档

## 📋 项目概述

### 背景
- **关键词发现**：发现新的有搜索量关键词 "nano banana prompt"
- **模型介绍**：Nano Banana 是 Google Gemini 2.5 Flash Image 模型的别名，专注于图片编辑而非生成
- **核心能力**：擅长对现有图片进行精确编辑，如对象添加/删除、背景处理、风格转换等

### 目标
在 Prompt Ark 的 prompt-library 下新增 nano-banana-prompt 页面，展示常用的 Nano Banana 提示词模板和示例。

## 🎯 用户需求分析

### 目标用户
- AI 图片编辑爱好者
- 设计师和创作者
- 寻找现成提示词模板的用户
- 学习 Nano Banana 使用技巧的用户

### 用户搜索意图
1. **学习使用方法**：如何使用 Nano Banana 进行图片编辑
2. **获取提示词模板**：寻找现成的、经过验证的 prompt 模板
3. **解决具体问题**：特定图片编辑任务的 prompt 写法
4. **查看效果示例**：了解 Nano Banana 的编辑能力和效果

## 🏗️ 页面设计方案

### 页面路径
```
/prompt-library/nano-banana-prompt
```

### 页面结构
```
Hero 区域
├── 标题：Nano Banana 最佳提示词库
├── 描述：精选图片编辑提示词模板
└── 快速导航按钮

ImageEditExamples 区域（核心）
├── 分类筛选（可选）
└── 示例展示网格

CTA 区域
├── 引导用户使用生成器
└── 相关功能推荐
```

### 单个示例卡片布局
```
┌─────────────────────────────────────────────────────────────────┐
│                    示例标题 + 分类标签                          │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐         ┌─────────────────┐                │
│  │     Before      │   -->   │      After      │                │
│  │    [原图]       │         │   [编辑后]      │                │
│  │   400x300px     │         │   400x300px     │                │
│  └─────────────────┘         └─────────────────┘                │
├─────────────────────────────────────────────────────────────────┤
│                          编辑说明                               │
├─────────────────────────────────────────────────────────────────┤
│  提示词内容区域                                    [复制按钮]     │
│  [代码块样式的prompt文本...]                                    │
└─────────────────────────────────────────────────────────────────┘
```

## 📱 响应式设计

### 桌面端（≥1024px）
- 每行一个示例，占满容器宽度
- 图片对比：左右并排，居中显示，图片尺寸 400x300px
- 信息区域：宽度与图片区域对齐

### 平板端（768px-1023px）
- 每行一个示例
- 图片对比：左右并排，图片尺寸 320x240px
- 信息区域：全宽显示

### 手机端（<768px）
- 每行一个示例
- 图片对比：上下堆叠或左右紧凑排列，图片尺寸 150x112px
- 信息区域：全宽显示

## 🔧 技术实现要点

### 新组件开发
需要创建新的 `ImageEditExamples` 组件，因为现有的 `PromptExamples` 组件是为视频设计的，不适合图片编辑场景。

### 数据结构
```typescript
interface ImageEditExample {
  id: string;
  title: string;
  description: string;
  category: string;
  beforeImage: string;
  afterImage: string;
  prompt: string;
  tags?: string[];
}

interface ImageEditExamplesSection {
  title: string;
  subtitle?: string;
  items: ImageEditExample[];
}
```

### 核心功能
1. **图片对比展示**：Before/After 图片并排显示
2. **提示词复制**：一键复制功能，带成功反馈
3. **图片预览**：点击图片可查看大图
4. **分类筛选**：按编辑类型筛选示例（可选）
5. **响应式布局**：适配各种设备尺寸

### 文件结构
```
app/[locale]/(default)/prompt-library/nano-banana-prompt/
├── page.tsx                           # 页面主文件
components/blocks/image-edit-examples/
├── index.tsx                          # 主组件
├── image-edit-card.tsx               # 单个示例卡片
types/blocks/
├── image-edit-examples.d.ts          # 类型定义
i18n/pages/nano-banana-prompt-library/
├── en.json                           # 英文配置
├── zh.json                           # 中文配置
```

## 📊 内容规划

### 提示词分类
1. **对象操作**：添加、删除、移动对象
2. **背景编辑**：背景替换、模糊、扩展
3. **色彩调整**：饱和度、亮度、色调
4. **风格转换**：艺术风格、滤镜效果
5. **细节优化**：清晰度、细节增强

### 示例内容要求
- **数量**：初期 15-20 个精选示例，每个分类 3-4 个
- **质量**：高质量的前后对比图片
- **实用性**：可直接使用的 prompt 模板
- **多样性**：覆盖不同使用场景和难度级别

### 单个示例包含
- **标题**：简洁描述编辑效果
- **分类标签**：明确的分类标识
- **Before 图片**：高质量原始图片
- **After 图片**：编辑后效果图
- **编辑说明**：2-3 句话描述具体修改
- **提示词**：完整的 Nano Banana prompt

## 🚀 SEO 优化

### 目标关键词
- nano banana prompt
- gemini 2.5 flash image prompt
- google image ai prompt
- 图片编辑 AI 提示词

### 页面优化
- 标题和描述包含核心关键词
- 图片 alt 标签优化
- 结构化数据标记
- 内部链接优化

## 📈 实施计划

### 第一阶段：基础实现
1. 创建 ImageEditExamples 组件
2. 实现页面基础结构
3. 准备 10-15 个示例内容
4. 完成响应式布局

### 第二阶段：功能完善
1. 添加图片预览功能
2. 实现分类筛选
3. 优化交互体验
4. 完善 SEO 设置

### 第三阶段：内容扩展
1. 增加更多示例
2. 添加用户反馈功能
3. 与其他页面联动
4. 数据分析和优化

## 🎯 成功指标

- 页面访问量和停留时间
- 提示词复制次数
- 用户反馈和评价
- 搜索引擎排名提升
- 转化到其他功能页面的比例
